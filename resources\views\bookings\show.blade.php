<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Booking Details') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    <h3 class="text-lg font-medium mb-4">Booking Reference: {{ $booking->booking_reference }}</h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <h4 class="font-medium mb-2">Trip Details</h4>
                            <p><strong>From:</strong> {{ $booking->trip->route->fromCity->name }}</p>
                            <p><strong>To:</strong> {{ $booking->trip->route->toCity->name }}</p>
                            <p><strong>Departure:</strong> {{ $booking->trip->departure_datetime->format('M d, Y H:i') }}</p>
                            <p><strong>Bus:</strong> {{ $booking->trip->bus->name }}</p>
                        </div>
                        
                        <div>
                            <h4 class="font-medium mb-2">Passenger Details</h4>
                            <p><strong>Name:</strong> {{ $booking->passenger_name }}</p>
                            <p><strong>Phone:</strong> {{ $booking->passenger_phone }}</p>
                            @if($booking->passenger_email)
                                <p><strong>Email:</strong> {{ $booking->passenger_email }}</p>
                            @endif
                        </div>
                    </div>
                    
                    <div class="mt-6">
                        <h4 class="font-medium mb-2">Seats</h4>
                        <div class="flex flex-wrap gap-2">
                            @foreach($booking->bookingSeats as $bookingSeat)
                                <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded">
                                    {{ $bookingSeat->seat->seat_number }}
                                </span>
                            @endforeach
                        </div>
                    </div>
                    
                    <div class="mt-6">
                        <p><strong>Total Amount:</strong> Rs. {{ number_format($booking->total_amount, 2) }}</p>
                        <p><strong>Status:</strong> 
                            <span class="px-2 py-1 rounded text-sm {{ $booking->status === 'booked' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                {{ ucfirst($booking->status) }}
                            </span>
                        </p>
                    </div>
                    
                    @if($booking->payments->count() > 0)
                        <div class="mt-6">
                            <h4 class="font-medium mb-2">Payment History</h4>
                            @foreach($booking->payments as $payment)
                                <div class="border rounded p-3 mb-2">
                                    <p><strong>Method:</strong> {{ $payment->method }}</p>
                                    <p><strong>Amount:</strong> Rs. {{ number_format($payment->amount, 2) }}</p>
                                    <p><strong>Status:</strong> {{ ucfirst($payment->payment_status) }}</p>
                                    @if($payment->transaction_id)
                                        <p><strong>Transaction ID:</strong> {{ $payment->transaction_id }}</p>
                                    @endif
                                </div>
                            @endforeach
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
